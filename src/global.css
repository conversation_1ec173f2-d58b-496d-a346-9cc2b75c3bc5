@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --brand-primary: 230 42% 28%;
    --brand-secondary: 0 100% 50%;
    --brand-ternary: 0 100% 70%;
    --primary-foreground: 0 0% 98%;
    --secondary: 224 17% 87%;
    --secondary-foreground: 230 42% 28%;
    --muted: 224 17% 87%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 224 17% 87%;
    --accent-foreground: 230 42% 28%;
    --danger: 0 84.2% 60.2%;
    --danger-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  /* .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --danger: 0 62.8% 30.6%;
    --danger-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  } */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  img {
    display: inline-block; /* fix stye conflict between bs and tailwind */
  }
}

@layer components {
  .glow-effect {
    position: relative;
    overflow: hidden;
    animation: glow-pulse 2s ease-in-out infinite;
  }

  .glow-effect::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 32px;
    height: 100%;
    background: linear-gradient(
      45deg,
      transparent,
      rgba(255, 255, 255, 0.9),
      transparent
    );
    transform: skew(-20deg);
    opacity: 0.6;
    animation: light-sweep 2s ease-in-out infinite;
    pointer-events: none;
  }

  @keyframes glow-pulse {
    0%,
    100% {
      box-shadow:
        0 0 5px rgba(255, 255, 255, 0.5),
        0 0 10px rgba(255, 255, 255, 0.3),
        0 0 15px rgba(255, 255, 255, 0.2);
    }
    50% {
      box-shadow:
        0 0 10px rgba(255, 255, 255, 0.8),
        0 0 20px rgba(255, 255, 255, 0.6),
        0 0 30px rgba(255, 255, 255, 0.4);
    }
  }

  @keyframes light-sweep {
    0% {
      transform: translateX(-100%) skew(-20deg);
    }
    100% {
      transform: translateX(400%) skew(-20deg);
    }
  }
}
