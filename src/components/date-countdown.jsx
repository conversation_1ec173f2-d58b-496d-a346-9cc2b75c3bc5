"use client";
import moment from "moment";
import { useEffect, useState } from "react";

export default function DateCountdown({ date }) {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false,
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = moment();
      const end = moment(new Date(date));
      const duration = moment.duration(end.diff(now));

      if (duration.asSeconds() <= 0) {
        return { isExpired: true };
      }

      return {
        days: Math.floor(duration.asDays()),
        hours: duration.hours(),
        minutes: duration.minutes(),
        seconds: duration.seconds(),
        isExpired: false,
      };
    };

    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [date]);

  if (timeLeft.isExpired) {
    return null;
  }

  return (
    <div className="flex items-center gap-1">
      <div className="flex flex-col items-center justify-between py-1 px-2 bg-white text-brand-primary rounded-sm">
        <span className="font-black">
          {timeLeft.days.toString().padStart(2, "0")}
        </span>
        <span className="text-[0.65rem]">J</span>
      </div>
      <span className="text-white">:</span>
      <div className="flex flex-col items-center justify-between py-1 px-2 bg-white text-brand-primary rounded-sm">
        <span className="font-black">
          {timeLeft.hours.toString().padStart(2, "0")}
        </span>
        <span className="text-[0.65rem]">H</span>
      </div>
      <span className="text-white">:</span>
      <div className="flex flex-col items-center justify-between py-1 px-2 bg-white text-brand-primary rounded-sm">
        <span className="font-black">
          {timeLeft.minutes.toString().padStart(2, "0")}
        </span>
        <span className="text-[0.65rem]">Min</span>
      </div>
      <span className="text-white">:</span>
      <div className="flex flex-col items-center justify-between py-1 px-2 bg-white text-brand-primary rounded-sm">
        <span className="font-black" key={timeLeft.seconds}>
          {timeLeft.seconds.toString().padStart(2, "0")}
        </span>
        <span className="text-[0.65rem]">Sec</span>
      </div>
    </div>
  );
}
