"use client";

import ProductsSlide from "@/components/products-slide";
import { useEffect, useState } from "react";

import DateCountdown from "@/components/date-countdown";
import { Button } from "@/components/ui/button";
import { ChevronRightCircleIcon } from "lucide-react";
import moment from "moment";
import Link from "next/link";

export default function Offers({ offers }) {
  if (!offers?.length) return null;

  return (
    <div className="space-y-10">
      {offers.map((offer) => (
        <OfferSection key={offer.id} offer={offer} />
      ))}
    </div>
  );
}

function OfferSection({ offer }) {
  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    isExpired: false,
  });

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = moment();
      const end = moment(offer.end_at);
      const duration = moment.duration(end.diff(now));

      if (duration.asSeconds() <= 0) {
        return { isExpired: true };
      }

      return {
        days: Math.floor(duration.asDays()),
        hours: duration.hours(),
        minutes: duration.minutes(),
        seconds: duration.seconds(),
        isExpired: false,
      };
    };

    setTimeLeft(calculateTimeLeft());

    // Update every second
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);

    return () => clearInterval(timer);
  }, [offer.end_at]);

  if (timeLeft.isExpired) {
    return null;
  }

  return (
    <section>
      <div
        style={{
          backgroundColor: offer.color,
        }}
        className="p-2 px-3 !rounded !rounded-b-none flex items-center justify-between"
      >
        <h3 className="text-white font-black text-xl sm:text-2xl">
          {offer.title}
        </h3>
        <div className="flex items-center gap-2 text-white">
          <span className="font-bold text-lg hidden sm:block">
            Temps restant:
          </span>
          <DateCountdown date={offer.end_at} />
        </div>
      </div>
      <div
        className="p-3 !border-2 rounded-b [&>section]:!p-0"
        style={{
          borderColor: offer.color,
        }}
      >
        <ProductsSlide
          products={offer.items?.map((item) => item.product)}
          autoplay
        />
      </div>
      <div className="flex justify-center mt-4">
        <Button
          variant="secondary"
          size="lg"
          style={{ backgroundColor: offer.color }}
          className="px-14 text-white rounded-full"
          asChild
        >
          <Link href={`/products?offer=${offer.slug}`}>
            Profitez-en <ChevronRightCircleIcon />
          </Link>
        </Button>
      </div>
    </section>
  );
}
